{"version": 3, "sources": ["../../.pnpm/element-plus@2.10.1_vue@3.5.16/node_modules/element-plus/dist/locale/en.mjs"], "sourcesContent": ["/*! Element Plus v2.10.1 */\n\nvar en = {\n  name: \"en\",\n  el: {\n    breadcrumb: {\n      label: \"Breadcrumb\"\n    },\n    colorpicker: {\n      confirm: \"OK\",\n      clear: \"Clear\",\n      defaultLabel: \"color picker\",\n      description: \"current color is {color}. press enter to select a new color.\",\n      alphaLabel: \"pick alpha value\"\n    },\n    datepicker: {\n      now: \"Now\",\n      today: \"Today\",\n      cancel: \"Cancel\",\n      clear: \"Clear\",\n      confirm: \"OK\",\n      dateTablePrompt: \"Use the arrow keys and enter to select the day of the month\",\n      monthTablePrompt: \"Use the arrow keys and enter to select the month\",\n      yearTablePrompt: \"Use the arrow keys and enter to select the year\",\n      selectedDate: \"Selected date\",\n      selectDate: \"Select date\",\n      selectTime: \"Select time\",\n      startDate: \"Start Date\",\n      startTime: \"Start Time\",\n      endDate: \"End Date\",\n      endTime: \"End Time\",\n      prevYear: \"Previous Year\",\n      nextYear: \"Next Year\",\n      prevMonth: \"Previous Month\",\n      nextMonth: \"Next Month\",\n      year: \"\",\n      month1: \"January\",\n      month2: \"February\",\n      month3: \"March\",\n      month4: \"April\",\n      month5: \"May\",\n      month6: \"June\",\n      month7: \"July\",\n      month8: \"August\",\n      month9: \"September\",\n      month10: \"October\",\n      month11: \"November\",\n      month12: \"December\",\n      week: \"week\",\n      weeks: {\n        sun: \"Sun\",\n        mon: \"Mon\",\n        tue: \"Tue\",\n        wed: \"Wed\",\n        thu: \"Thu\",\n        fri: \"Fri\",\n        sat: \"Sat\"\n      },\n      weeksFull: {\n        sun: \"Sunday\",\n        mon: \"Monday\",\n        tue: \"Tuesday\",\n        wed: \"Wednesday\",\n        thu: \"Thursday\",\n        fri: \"Friday\",\n        sat: \"Saturday\"\n      },\n      months: {\n        jan: \"Jan\",\n        feb: \"Feb\",\n        mar: \"Mar\",\n        apr: \"Apr\",\n        may: \"May\",\n        jun: \"Jun\",\n        jul: \"Jul\",\n        aug: \"Aug\",\n        sep: \"Sep\",\n        oct: \"Oct\",\n        nov: \"Nov\",\n        dec: \"Dec\"\n      }\n    },\n    inputNumber: {\n      decrease: \"decrease number\",\n      increase: \"increase number\"\n    },\n    select: {\n      loading: \"Loading\",\n      noMatch: \"No matching data\",\n      noData: \"No data\",\n      placeholder: \"Select\"\n    },\n    mention: {\n      loading: \"Loading\"\n    },\n    dropdown: {\n      toggleDropdown: \"Toggle Dropdown\"\n    },\n    cascader: {\n      noMatch: \"No matching data\",\n      loading: \"Loading\",\n      placeholder: \"Select\",\n      noData: \"No data\"\n    },\n    pagination: {\n      goto: \"Go to\",\n      pagesize: \"/page\",\n      total: \"Total {total}\",\n      pageClassifier: \"\",\n      page: \"Page\",\n      prev: \"Go to previous page\",\n      next: \"Go to next page\",\n      currentPage: \"page {pager}\",\n      prevPages: \"Previous {pager} pages\",\n      nextPages: \"Next {pager} pages\",\n      deprecationWarning: \"Deprecated usages detected, please refer to the el-pagination documentation for more details\"\n    },\n    dialog: {\n      close: \"Close this dialog\"\n    },\n    drawer: {\n      close: \"Close this dialog\"\n    },\n    messagebox: {\n      title: \"Message\",\n      confirm: \"OK\",\n      cancel: \"Cancel\",\n      error: \"Illegal input\",\n      close: \"Close this dialog\"\n    },\n    upload: {\n      deleteTip: \"press delete to remove\",\n      delete: \"Delete\",\n      preview: \"Preview\",\n      continue: \"Continue\"\n    },\n    slider: {\n      defaultLabel: \"slider between {min} and {max}\",\n      defaultRangeStartLabel: \"pick start value\",\n      defaultRangeEndLabel: \"pick end value\"\n    },\n    table: {\n      emptyText: \"No Data\",\n      confirmFilter: \"Confirm\",\n      resetFilter: \"Reset\",\n      clearFilter: \"All\",\n      sumText: \"Sum\"\n    },\n    tour: {\n      next: \"Next\",\n      previous: \"Previous\",\n      finish: \"Finish\"\n    },\n    tree: {\n      emptyText: \"No Data\"\n    },\n    transfer: {\n      noMatch: \"No matching data\",\n      noData: \"No data\",\n      titles: [\"List 1\", \"List 2\"],\n      filterPlaceholder: \"Enter keyword\",\n      noCheckedFormat: \"{total} items\",\n      hasCheckedFormat: \"{checked}/{total} checked\"\n    },\n    image: {\n      error: \"FAILED\"\n    },\n    pageHeader: {\n      title: \"Back\"\n    },\n    popconfirm: {\n      confirmButtonText: \"Yes\",\n      cancelButtonText: \"No\"\n    },\n    carousel: {\n      leftArrow: \"Carousel arrow left\",\n      rightArrow: \"Carousel arrow right\",\n      indicator: \"Carousel switch to index {index}\"\n    }\n  }\n};\n\nexport { en as default };\n"], "mappings": ";;;AAEA,IAAI,KAAK;AAAA,EACP,MAAM;AAAA,EACN,IAAI;AAAA,IACF,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,MACP,cAAc;AAAA,MACd,aAAa;AAAA,MACb,YAAY;AAAA,IACd;AAAA,IACA,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,MACA,WAAW;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IACF;AAAA,IACA,aAAa;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,aAAa;AAAA,IACf;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,QAAQ;AAAA,IACV;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,WAAW;AAAA,MACX,WAAW;AAAA,MACX,oBAAoB;AAAA,IACtB;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,cAAc;AAAA,MACd,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,IACxB;AAAA,IACA,OAAO;AAAA,MACL,WAAW;AAAA,MACX,eAAe;AAAA,MACf,aAAa;AAAA,MACb,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,WAAW;AAAA,IACb;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ,CAAC,UAAU,QAAQ;AAAA,MAC3B,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,IACpB;AAAA,IACA,OAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,MACR,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,EACF;AACF;", "names": []}